

import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const CHAT_BASE_PATH = path.join(process.cwd(), 'data', 'app', 'chat-history');

export async function DELETE(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, sessionId } = body;

    if (!userId || !sessionId) {
      return NextResponse.json({ error: 'Missing userId or sessionId' }, { status: 400 });
    }

    const filePath = path.join(CHAT_BASE_PATH, String(userId), `${sessionId}.json`);

    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    fs.unlinkSync(filePath);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('DELETE chat history failed:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
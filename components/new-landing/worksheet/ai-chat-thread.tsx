'use client';

import { useEffect, useRef, useState } from 'react';

interface ChatMessage {
  id: string;
  role: 'user' | 'ai';
  content: string;
  createdAt: string;
  deleted?: boolean;
}

export interface AiChatThreadProps {
  prompt: string;
  userType: 'freelancer' | 'commissioner';
  onAddMessage?: (message: ChatMessage) => void;
}

// Create a simple event system for component communication
export const chatEvents = {
  addMessage: (message: ChatMessage) => {
    window.dispatchEvent(new CustomEvent('addChatMessage', { detail: message }));
  }
};

// Utility to validate content is user-safe
function isValidContent(str: string): boolean {
  if (!str || typeof str !== 'string') return false;

  // Check for JSON-like content
  if (str.startsWith('{') || str.startsWith('[')) return false;

  // Check for API debug keys
  if (str.includes('opportunities_results') ||
      str.includes('freelancer_selection') ||
      str.includes('step:') ||
      str.includes('"message"') ||
      str.includes('"result"')) return false;

  // Check for overly long responses (likely raw data)
  if (str.length > 1000) return false;

  return true;
}

// Smart intent classification for local overrides
function classifyIntent(prompt: string, previousMessages: ChatMessage[] = []): string {
  const lowerPrompt = prompt.toLowerCase().trim();

  // Gratitude
  if (lowerPrompt.includes('thank') || lowerPrompt.includes('thanks')) {
    return 'gratitude';
  }

  // Greetings
  if (lowerPrompt.match(/^(hi|hello|hey|good morning|good afternoon)/)) {
    return 'greeting';
  }

  // Gig posting
  if (lowerPrompt.includes('post') && (lowerPrompt.includes('gig') || lowerPrompt.includes('job'))) {
    return 'post_gig';
  }

  // Custom proposal ideas - enhanced with context
  if (lowerPrompt.includes('idea') || lowerPrompt.includes('brainstorm') ||
      (lowerPrompt.includes('custom') && lowerPrompt.includes('proposal'))) {
    return 'custom_proposal_ideas';
  }

  // Mobile app context
  if (lowerPrompt.includes('mobile') || lowerPrompt.includes('app')) {
    // Check if previous messages mentioned custom proposals
    const hasProposalContext = previousMessages.some(msg =>
      msg.content.toLowerCase().includes('custom proposal') ||
      msg.content.toLowerCase().includes('idea')
    );
    if (hasProposalContext) {
      return 'mobile_app_ideas';
    }
    return 'mobile_app';
  }

  // Commissioner research
  if (lowerPrompt.includes('who') && (lowerPrompt.includes('commission') || lowerPrompt.includes('hire'))) {
    return 'commissioner_research';
  }

  return 'general';
}

// Utility functions for message handling
function generateMessageId(): string {
  return Date.now().toString() + Math.random().toString(36).substring(2, 11);
}

function createMessage(role: 'user' | 'ai', content: string): ChatMessage {
  return {
    id: generateMessageId(),
    role,
    content,
    createdAt: new Date().toISOString(),
    deleted: false
  };
}

function formatRelativeTime(timestamp: string): string {
  const now = new Date();
  const messageTime = new Date(timestamp);
  const diffMs = now.getTime() - messageTime.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return 'just now';
  if (diffMins < 60) return `${diffMins} min ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  return messageTime.toLocaleDateString();
}

function formatAbsoluteTime(timestamp: string): string {
  return new Date(timestamp).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}

export default function AiChatThread({ prompt, userType }: AiChatThreadProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    createMessage('user', prompt)
  ]);
  const [loading, setLoading] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const bottomRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        e.preventDefault();
        if (newMessage.trim()) {
          handleSendMessage();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [newMessage]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  // Listen for messages from other components
  useEffect(() => {
    const handleAddMessage = (event: CustomEvent<ChatMessage>) => {
      setMessages(prev => [...prev, event.detail]);
    };

    const handleGigSelection = (event: CustomEvent<any>) => {
      const gig = event.detail;
      const dynamicMessage = `Here's a quick pitch idea for the ${gig.projectName} opportunity focused on ${gig.skillsRequired.slice(0, 2).join(', ')}, and your past work experience.

✨ I drafted a quick pitch for you! Feel free to edit, add a portfolio link, and send when you're ready 🚀

Want to create a custom proposal instead?`;

      const messageWithButton = `${dynamicMessage}

<div style="margin-top: 12px;">
  <button onclick="window.location.href='/freelancer-dashboard/projects-and-invoices/create-proposal/proposal-preview?gigId=${gig.gigId}'"
          style="background: #eb1966; color: white; padding: 8px 16px; border-radius: 8px; border: none; font-size: 14px; cursor: pointer; font-family: Plus Jakarta Sans;">
    Open Proposal Form
  </button>
</div>`;

      setMessages(prev => [...prev, createMessage('ai', messageWithButton)]);
    };

    window.addEventListener('addChatMessage', handleAddMessage as EventListener);
    window.addEventListener('gigSelected', handleGigSelection as EventListener);
    return () => {
      window.removeEventListener('addChatMessage', handleAddMessage as EventListener);
      window.removeEventListener('gigSelected', handleGigSelection as EventListener);
    };
  }, []);

  // Auto-create chat history on prompt start
  useEffect(() => {
    const createChatHistory = async () => {
      // Get session data
      const session = await fetch('/api/auth/session').then(res => res.json());
      if (!session?.user?.id) return;

      try {
        await fetch('/api/app/chat-history/create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: session.user.id,
            prompt,
            userType,
            timestamp: new Date().toISOString()
          })
        });
        console.log('✅ Chat history created successfully');

        // Notify chat history panel to refresh
        window.dispatchEvent(new CustomEvent('newChatSession'));
      } catch (error) {
        console.error('❌ Failed to create chat history:', error);
      }
    };

    if (prompt.trim()) {
      createChatHistory();
    }
  }, [prompt, userType]);

  // Initialize chat with AI response to the prompt
  useEffect(() => {
    if (!prompt.trim()) return;

    const initializeChat = async () => {
      setLoading(true);
      try {
        // Check for local intent overrides first
        const intent = classifyIntent(prompt, messages);
        let friendlyMessage = '';

        switch (intent) {
          case 'gratitude':
            friendlyMessage = "You're welcome! Let me know how else I can help 🌟";
            break;
          case 'greeting':
            friendlyMessage = "Hello! I'm here to help you find amazing creative opportunities. What are you looking to work on?";
            break;
          case 'post_gig':
            friendlyMessage = "Let's get your creative brief in shape. What's the title of your gig?";
            break;
          case 'custom_proposal_ideas':
            friendlyMessage = `Let's build something amazing. What's the core idea you want to pitch?

Here are some creative directions to consider:
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Brand Identity Design</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">User Experience Audit</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Content Strategy</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Digital Marketing Campaign</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Product Photography</span>`;
            break;
          case 'mobile_app_ideas':
            friendlyMessage = `Perfect! Mobile apps are exciting projects. Here are some focused ideas for your custom proposal:

<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">iOS/Android UI Design</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">User Flow Optimization</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">App Icon & Branding</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Prototype Development</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">App Store Assets</span>

What type of mobile app are you thinking about?`;
            break;
          case 'commissioner_research':
            friendlyMessage = "I can help you research recent commissioners! Let me check who's been posting creative projects lately.";
            break;
          default:
            // Make API call for general queries
            const apiEndpoint = userType === 'freelancer'
              ? '/api/ai-intake/freelancer'
              : '/api/ai-intake/client';

            const res = await fetch(apiEndpoint, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                intent: prompt,
                step: 'initial',
                prompt: userType === 'commissioner' ? prompt : undefined
              }),
            });

            const data = await res.json();
            const resultText = typeof data.result === 'string' ? data.result : '';

            // Validate content before showing to user
            if (isValidContent(resultText)) {
              friendlyMessage = resultText;
            } else {
              // Try to parse structured response
              try {
                const parsedResult = JSON.parse(data.result || '{}');
                if (parsedResult.opportunities && Array.isArray(parsedResult.opportunities)) {
                  const count = parsedResult.opportunities.length;
                  friendlyMessage = `✨ Found ${count} matching opportunit${count !== 1 ? 'ies' : 'y'} for you! Check them out in the worksheet.`;
                } else {
                  friendlyMessage = "Hmm, I'm not sure how to help with that yet — want to try asking differently?";
                }
              } catch {
                friendlyMessage = "Hmm, I'm not sure how to help with that yet — want to try asking differently?";
              }
            }
        }

        // Add AI response to chat, avoiding duplicates and similar gig-matching messages
        setMessages(prev => {
          const aiMessage = createMessage('ai', friendlyMessage);

          // Check for exact duplicates
          const isDuplicate = prev.some(msg => msg.role === 'ai' && msg.content === friendlyMessage);
          if (isDuplicate) return prev;

          // Check for similar gig-matching messages to prevent redundancy
          const hasGigMatchMessage = prev.some(msg =>
            msg.role === 'ai' && (
              msg.content.includes('gigs that might be a perfect fit') ||
              msg.content.includes('see if any of these') ||
              msg.content.includes('based on your profile') ||
              msg.content.includes('checked the marketplace') ||
              msg.content.includes('might catch your creative spark') ||
              msg.content.includes('interesting opportunit') ||
              msg.content.includes('could be your next big project') ||
              msg.content.includes('potential match')
            )
          );

          // If we already have a gig-matching message and this is another one, skip it
          const isGigMatchMessage = friendlyMessage.includes('gigs that might be a perfect fit') ||
                                   friendlyMessage.includes('see if any of these') ||
                                   friendlyMessage.includes('based on your profile') ||
                                   friendlyMessage.includes('checked the marketplace') ||
                                   friendlyMessage.includes('might catch your creative spark') ||
                                   friendlyMessage.includes('interesting opportunit') ||
                                   friendlyMessage.includes('could be your next big project') ||
                                   friendlyMessage.includes('potential match');

          if (hasGigMatchMessage && isGigMatchMessage) {
            return prev; // Skip adding another similar gig-matching message
          }

          return [...prev, aiMessage];
        });
      } catch (error) {
        console.error('Failed to initialize chat:', error);
        setMessages(prev => [...prev, createMessage('ai', 'Sorry, I encountered an error. Please try again.')]);
      } finally {
        setLoading(false);
      }
    };

    initializeChat();
  }, [prompt, userType]);

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    // Add user message to chat
    const userMessage = createMessage('user', newMessage);
    setMessages(prev => [...prev, userMessage]);

    const messageToSend = newMessage;
    setNewMessage('');
    setLoading(true);

    try {
      // Check for local intent overrides first (with chat memory)
      const recentMessages = messages.slice(-5); // Last 5 messages for context
      const intent = classifyIntent(messageToSend, recentMessages);
      let friendlyResponse = '';

      switch (intent) {
        case 'gratitude':
          friendlyResponse = "You're welcome! Let me know how else I can help 🌟";
          break;
        case 'greeting':
          friendlyResponse = "Hello! I'm here to help you find amazing creative opportunities. What are you looking to work on?";
          break;
        case 'post_gig':
          friendlyResponse = "Let's get your creative brief in shape. What's the title of your gig?";
          break;
        case 'custom_proposal_ideas':
          friendlyResponse = `Let's build something amazing. What's the core idea you want to pitch?

Here are some creative directions to consider:
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Brand Identity Design</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">User Experience Audit</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Content Strategy</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Digital Marketing Campaign</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Product Photography</span>`;
          break;
        case 'mobile_app_ideas':
          friendlyResponse = `Perfect! Mobile apps are exciting projects. Here are some focused ideas for your custom proposal:

<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">iOS/Android UI Design</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">User Flow Optimization</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">App Icon & Branding</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">Prototype Development</span>
<span class="bg-gray-100 text-xs rounded-full px-2 py-1 mr-2 mb-1 inline-block">App Store Assets</span>

What type of mobile app are you thinking about?`;
          break;
        case 'commissioner_research':
          friendlyResponse = "I can help you research recent commissioners! Let me check who's been posting creative projects lately.";
          break;
        default:
          // Make API call for general queries
          const apiEndpoint = userType === 'freelancer'
            ? '/api/ai-intake/freelancer'
            : '/api/ai-intake/client';

          const res = await fetch(apiEndpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              intent: messageToSend,
              step: 'followup'
            }),
          });

          const data = await res.json();
          const resultText = typeof data.result === 'string' ? data.result : '';

          // Validate content before showing to user
          if (isValidContent(resultText)) {
            friendlyResponse = resultText;
          } else {
            friendlyResponse = "Hmm, I'm not sure how to help with that yet — want to try asking differently?";
          }
      }

      setMessages(prev => [...prev, createMessage('ai', friendlyResponse)]);
    } catch (error) {
      console.error('Failed to send message:', error);
      setMessages(prev => [...prev, createMessage('ai', 'Sorry, I encountered an error. Please try again.')]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.filter(msg => !msg.deleted).map((msg) => (
          <div
            key={msg.id}
            className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`px-4 py-3 rounded-lg shadow max-w-[80%] relative group ${
                msg.role === 'user'
                  ? 'bg-black text-white self-end'
                  : 'bg-white text-black border border-gray-200'
              }`}
              style={{ fontFamily: 'Plus Jakarta Sans' }}
            >
              <div className="pr-16" dangerouslySetInnerHTML={{ __html: msg.content }} />

              {/* Copy button for AI messages */}
              {msg.role === 'ai' && (
                <button
                  onClick={() => copyToClipboard(msg.content)}
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-100 rounded"
                  title="Copy message"
                >
                  <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              )}

              <div
                className="absolute bottom-2 right-2 text-xs text-gray-400 cursor-help"
                title={formatAbsoluteTime(msg.createdAt)}
              >
                {formatRelativeTime(msg.createdAt)}
              </div>
            </div>
          </div>
        ))}
        {loading && (
          <div className="flex justify-start">
            <div className="bg-white text-black border border-gray-200 px-4 py-3 rounded-lg max-w-[80%]">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
                <span>AI is thinking...</span>
              </div>
            </div>
          </div>
        )}
        <div ref={bottomRef} />
      </div>

      {/* Sticky Input Field */}
      <div className="sticky bottom-0 bg-white p-3 border-t border-gray-200">
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Ask me anything... (⌘+Enter to send)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 text-sm"
            style={{ fontFamily: 'Plus Jakarta Sans' }}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && newMessage.trim()) {
                handleSendMessage();
              }
            }}
          />
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || loading}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              newMessage.trim() && !loading
                ? 'bg-black text-white hover:bg-gray-800'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
            style={{ fontFamily: 'Plus Jakarta Sans' }}
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
